INFO - [32m📧 创建WQB会话: <EMAIL>[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32m✅ WQB认证成功! 状态码: 200[0m
INFO - [32m📊 槽位启动延迟计划: ['0.0s', '4.1s', '9.1s', '8.9s', '21.9s', '42.8s', '69.4s', '50.1s'][0m
INFO - [32m🚀 优化MultiAlpha流水线系统初始化[0m
INFO - [32m📁 目标文件: decoded_expressions (25).txt[0m
INFO - [32m🎲 随机种子: 42[0m
INFO - [32m⚙️ 配置: 8Multi槽位, 10Alpha/批次[0m
INFO - [32m🛡️ 保护延迟: 2.0秒/MultiAlpha请求[0m
INFO - [32m📊 槽位启动策略: 指数退避[0m
INFO - [32m正常工作1[0m
INFO - [32m🚀 启动优化MultiAlpha流水线系统[0m
INFO - [32m================================================================================[0m
INFO - [32m🔥 开始请求预热...[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32m✅ 预热请求成功[0m
INFO - [32m📁 加载Alpha因子...[0m
INFO - [32m📁 开始加载因子文件: decoded_expressions (25).txt[0m
INFO - [32m✅ 成功加载 49800 个Alpha因子 (txt单列模式)[0m
INFO - [32m📋 前5个因子示例:[0m
INFO - [32m  1. 1: ts_zscore(act_12m_net_value/act_12m_bps_value, 20)[0m
INFO - [32m  2. 2: ts_zscore(act_12m_net_value/act_12m_bps_value, 100...[0m
INFO - [32m  3. 3: ts_zscore(act_12m_net_value/act_12m_bps_value, 200...[0m
INFO - [32m  4. 4: ts_zscore(act_12m_net_value/act_12m_bps_value, 400...[0m
INFO - [32m  5. 5: ts_zscore(act_12m_eps_value/act_12m_bps_value, 20)[0m
INFO - [32m💡 去除 30920 个重复Alpha表达式[0m
INFO - [32m📦 创建MultiAlpha任务，每批10个Alpha[0m
INFO - [32m✅ 创建1888个任务 (编号1-1888)[0m
INFO - [32m🚀 开始渐进式启动 8 个槽位...[0m
INFO - [32m🔧 槽位0 启动[0m
INFO - [32m🔧 槽位1 等待 4.1 秒后启动...[0m
INFO - [32m🔧 槽位2 等待 9.1 秒后启动...[0m
INFO - [32m🔧 槽位3 等待 8.9 秒后启动...[0m
INFO - [32m🔧 槽位4 等待 21.9 秒后启动...[0m
INFO - [32m🔧 槽位5 等待 42.8 秒后启动...[0m
INFO - [32m🔧 槽位6 等待 69.4 秒后启动...[0m
INFO - [32m🔧 槽位7 等待 50.1 秒后启动...[0m
INFO - [32m🔧 槽位1 启动[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 0/1888 (0.0%) | 成功率: 0.0%[0m
INFO - [32m⚡ 唯一效率: 0.0 唯一Alpha/分钟 | 唯一提取: 0/18880[0m
INFO - [32m🔧 槽位: 2/8 | 待处理: 1886 | 失败: 0[0m
INFO - [32m📡 API频率: 1/5 (20.0%) | 🚀 智能并行: 4槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 计算中...[0m
INFO - [32m============================================================[0m
INFO - [32m🔧 槽位3 启动[0m
INFO - [32m🔧 槽位2 启动[0m
INFO - [32m🔧 槽位4 启动[0m
ERROR - [31m❌ [槽位0] 任务1失败: {'children': ['1qbP5B6RC52oc1c15l71Qpie', '4dYd2y9fv58GcxWKzEcfhdm', 'EmMdB68o52YbjxuOMXd6CI', 'oxl0FgD74KB8Ht15vnk6U4l', 'ChfWz5jh52Kahu3UbetleM', '1EQ2fnbLZ566aGPKEshNpUR', '1MYtB93hU4X38Vt1a5efP1so', '19446H1G4Lia4FTaNwok6b', '2AV7vevt4zzaQhXbfMZGd9', '4vBG7E8Gy52abqZal5WHSvd'], 'type': 'REGULAR', 'status': 'ERROR'}[0m
INFO - [32m🔍 [槽位0] 开始自动错误诊断...[0m
INFO - [32m🔍 [槽位0] 开始错误快速定位：https://api.worldquantbrain.com/simulations/12sYx3eYx5iNaSi2Hv0CuCB[0m
INFO - [32m🔍 [槽位0] MultiAlpha状态: ERROR, Children数量: 10[0m
INFO - [32m🔍 [槽位0] 开始并发错误诊断，共10个child， 并发度 1[0m
INFO - [32m✅ [槽位0] 错误定位完成![0m
INFO - [32m📊 [槽位0] 统计: 0个CANCELLED, 1个错误源已定位[0m
ERROR - [31m🎯 [槽位0] 已定位到具体错误Alpha，详情见上方日志[0m
INFO - [32m🧠 [槽位0] 启动智能错误恢复...[0m
INFO - [32m🧠 [槽位0] 开始智能错误恢复：任务1[0m
INFO - [32m🔍 [槽位0] 分析10个Alpha的状态...[0m
WARNING - [33m❌ [槽位0] Alpha 1: ts_zscore(act_12m_net_value/act_12m_bps_value, 20)... - 真正错误: ERROR - Attempted to use unknown variable "act_12m_net_value"[0m
WARNING - [33m❌ [槽位0] Alpha 2: ts_zscore(act_12m_net_value/act_12m_bps_value, 100... - 真正错误: ERROR - Attempted to use unknown variable "act_12m_net_value"[0m
WARNING - [33m❌ [槽位0] Alpha 3: ts_zscore(act_12m_net_value/act_12m_bps_value, 200... - 真正错误: ERROR - Attempted to use unknown variable "act_12m_net_value"[0m
WARNING - [33m❌ [槽位0] Alpha 4: ts_zscore(act_12m_net_value/act_12m_bps_value, 400... - 真正错误: ERROR - Attempted to use unknown variable "act_12m_net_value"[0m
WARNING - [33m❌ [槽位0] Alpha 5: ts_zscore(act_12m_eps_value/act_12m_bps_value, 20)... - 真正错误: ERROR - Attempted to use unknown variable "act_12m_eps_value"[0m
