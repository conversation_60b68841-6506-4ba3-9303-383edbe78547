#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于经济学原理构造有意义的比值型合成指标
"""

import sqlite3
import pandas as pd
import re
from itertools import combinations

def get_matrix_fields():
    """获取所有MATRIX类型的基本面字段"""
    conn = sqlite3.connect('alphas.db')
    matrix_fields = pd.read_sql_query(
        "SELECT DISTINCT alpha_id FROM data_USA_1_TOP3000 WHERE type='MATRIX' ORDER BY alpha_id", 
        conn
    )
    conn.close()
    return matrix_fields['alpha_id'].tolist()

def categorize_fields(fields):
    """将字段按照财务指标类型分类"""
    categories = {
        # 盈利能力指标
        'profitability': {
            'earnings': [],  # 收益类
            'revenue': [],   # 收入类
            'margin': [],    # 利润率类
        },
        
        # 估值指标
        'valuation': {
            'price': [],     # 价格类
            'book': [],      # 账面价值类
            'market': [],    # 市值类
        },
        
        # 财务状况指标
        'financial_position': {
            'assets': [],    # 资产类
            'liabilities': [], # 负债类
            'equity': [],    # 权益类
            'cash': [],      # 现金类
        },
        
        # 运营效率指标
        'efficiency': {
            'turnover': [],  # 周转率类
            'growth': [],    # 增长率类
            'return': [],    # 回报率类
        },
        
        # 风险指标
        'risk': {
            'leverage': [],  # 杠杆类
            'volatility': [], # 波动性类
            'coverage': [],  # 覆盖率类
        }
    }
    
    # 关键词映射
    keyword_mapping = {
        # 盈利能力
        'profitability': {
            'earnings': ['eps', 'ebi', 'ebt', 'net', 'ner', 'opr'],
            'revenue': ['sal', 'rev', 'gps', 'grm'],
            'margin': ['mar', 'mgn', 'prr'],
        },
        
        # 估值
        'valuation': {
            'price': ['prc', 'pri', 'shr'],
            'book': ['bps', 'bvs', 'nav'],
            'market': ['mkt', 'cap', 'val'],
        },
        
        # 财务状况
        'financial_position': {
            'assets': ['ast', 'ass', 'tot'],
            'liabilities': ['dbt', 'lib', 'lbt'],
            'equity': ['equ', 'sha', 'stk'],
            'cash': ['csh', 'cas', 'fcf'],
        },
        
        # 运营效率
        'efficiency': {
            'turnover': ['tur', 'cyc'],
            'growth': ['grw', 'gro'],
            'return': ['roa', 'roe', 'roi'],
        },
        
        # 风险
        'risk': {
            'leverage': ['lev', 'deb'],
            'volatility': ['vol', 'std'],
            'coverage': ['cov', 'int'],
        }
    }
    
    # 分类字段
    for field in fields:
        field_lower = field.lower()
        categorized = False
        
        for main_cat, sub_cats in keyword_mapping.items():
            for sub_cat, keywords in sub_cats.items():
                for keyword in keywords:
                    if keyword in field_lower:
                        categories[main_cat][sub_cat].append(field)
                        categorized = True
                        break
                if categorized:
                    break
            if categorized:
                break
    
    return categories

def generate_ratio_combinations(categories):
    """基于经济学原理生成有意义的比值组合"""
    ratios = []
    
    # 1. 盈利能力比率
    # ROE类比率：收益/权益
    for earnings in categories['profitability']['earnings']:
        for equity in categories['financial_position']['equity']:
            ratios.append((earnings, equity, f"盈利能力-ROE类: {earnings}/{equity}"))
    
    # ROA类比率：收益/资产
    for earnings in categories['profitability']['earnings']:
        for assets in categories['financial_position']['assets']:
            ratios.append((earnings, assets, f"盈利能力-ROA类: {earnings}/{assets}"))
    
    # 利润率：收益/收入
    for earnings in categories['profitability']['earnings']:
        for revenue in categories['profitability']['revenue']:
            ratios.append((earnings, revenue, f"盈利能力-利润率: {earnings}/{revenue}"))
    
    # 2. 估值比率
    # P/E类比率：价格/收益
    for price in categories['valuation']['price']:
        for earnings in categories['profitability']['earnings']:
            ratios.append((price, earnings, f"估值-PE类: {price}/{earnings}"))
    
    # P/B类比率：价格/账面价值
    for price in categories['valuation']['price']:
        for book in categories['valuation']['book']:
            ratios.append((price, book, f"估值-PB类: {price}/{book}"))
    
    # 市值/收入比率
    for market in categories['valuation']['market']:
        for revenue in categories['profitability']['revenue']:
            ratios.append((market, revenue, f"估值-PS类: {market}/{revenue}"))
    
    # 3. 财务结构比率
    # 资产负债率：负债/资产
    for liabilities in categories['financial_position']['liabilities']:
        for assets in categories['financial_position']['assets']:
            ratios.append((liabilities, assets, f"财务结构-资产负债率: {liabilities}/{assets}"))
    
    # 权益比率：权益/资产
    for equity in categories['financial_position']['equity']:
        for assets in categories['financial_position']['assets']:
            ratios.append((equity, assets, f"财务结构-权益比率: {equity}/{assets}"))
    
    # 4. 运营效率比率
    # 资产周转率：收入/资产
    for revenue in categories['profitability']['revenue']:
        for assets in categories['financial_position']['assets']:
            ratios.append((revenue, assets, f"运营效率-资产周转率: {revenue}/{assets}"))
    
    # 权益周转率：收入/权益
    for revenue in categories['profitability']['revenue']:
        for equity in categories['financial_position']['equity']:
            ratios.append((revenue, equity, f"运营效率-权益周转率: {revenue}/{equity}"))
    
    # 5. 现金流比率
    # 现金流/收入
    for cash in categories['financial_position']['cash']:
        for revenue in categories['profitability']['revenue']:
            ratios.append((cash, revenue, f"现金流-现金流收入比: {cash}/{revenue}"))
    
    # 现金流/资产
    for cash in categories['financial_position']['cash']:
        for assets in categories['financial_position']['assets']:
            ratios.append((cash, assets, f"现金流-现金流资产比: {cash}/{assets}"))
    
    return ratios

def main():
    print("开始分析数据库中的基本面字段...")
    
    # 获取所有MATRIX字段
    fields = get_matrix_fields()
    print(f"总共找到 {len(fields)} 个MATRIX类型字段")
    
    # 分类字段
    categories = categorize_fields(fields)
    
    # 打印分类结果
    print("\n字段分类结果:")
    for main_cat, sub_cats in categories.items():
        print(f"\n{main_cat}:")
        for sub_cat, field_list in sub_cats.items():
            print(f"  {sub_cat}: {len(field_list)} 个字段")
            if field_list:
                print(f"    示例: {field_list[:3]}")
    
    # 生成比值组合
    print("\n开始生成有经济学意义的比值组合...")
    ratios = generate_ratio_combinations(categories)
    
    print(f"总共生成了 {len(ratios)} 个比值指标")
    
    # 保存第一批500个指标
    batch_size = 500
    batch_1 = ratios[:batch_size]
    
    with open('ratio_indicators_batch_1.txt', 'w', encoding='utf-8') as f:
        for numerator, denominator, description in batch_1:
            f.write(f"{numerator}/{denominator}\n")
    
    print(f"\n已保存第一批 {len(batch_1)} 个比值指标到 ratio_indicators_batch_1.txt")
    print("\n第一批指标示例:")
    for i, (num, den, desc) in enumerate(batch_1[:10]):
        print(f"{i+1:3d}. {num}/{den} - {desc}")
    
    return len(ratios)

if __name__ == "__main__":
    total_ratios = main()
    print(f"\n总计可生成 {total_ratios} 个比值指标")
    print("请回复'继续'以生成下一批500个指标")
