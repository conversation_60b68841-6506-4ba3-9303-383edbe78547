# INFO 2025-08-02 23:30:39,666
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-02 23:30:45,167
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# WARNING 2025-08-03 01:08:00,116
<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_prr_value/act_12m_nav_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_prr_value/act_12m_nav_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_prr_value/act_12m_nav_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_prr_value/act_12m_nav_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_ebi_value/act_12m_nav_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_ebi_value/act_12m_nav_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_ebi_value/act_12m_nav_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_ebi_value/act_12m_nav_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_ebt_value/act_12m_nav_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_ebt_value/act_12m_nav_value, 100)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.381882
    headers: {'Date': 'Sat, 02 Aug 2025 17:07:57 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '511c849d8bf74c739c5c6f18b5cc969b', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}

# INFO 2025-08-03 01:08:04,142
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# WARNING 2025-08-03 01:15:42,182
<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_ebi_value/act_12m_sal_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_ebi_value/act_12m_sal_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_ebt_value/act_12m_sal_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_ebt_value/act_12m_sal_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_ebt_value/act_12m_sal_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_ebt_value/act_12m_sal_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_ner_value/act_12m_sal_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_ner_value/act_12m_sal_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_ner_value/act_12m_sal_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_ner_value/act_12m_sal_value, 400)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.390932
    headers: {'Date': 'Sat, 02 Aug 2025 17:15:39 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '892b722b32fa4dca81cc6551e2a91b4a', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}

# INFO 2025-08-03 01:15:46,191
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# WARNING 2025-08-03 01:19:27,066
<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_ebt_value/act_q_sal_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_ebt_value/act_q_sal_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_ebt_value/act_q_sal_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_ebt_value/act_q_sal_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_ner_value/act_q_sal_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_ner_value/act_q_sal_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_ner_value/act_q_sal_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_ner_value/act_q_sal_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_cps_value/act_12m_sal_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_cps_value/act_12m_sal_value, 100)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.408243
    headers: {'Date': 'Sat, 02 Aug 2025 17:19:24 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '7a99a3a8f93d4cff83551583abee6c78', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}

# INFO 2025-08-03 01:19:30,898
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# WARNING 2025-08-03 01:20:41,938
<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_ebt_value/act_q_sal_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_ebt_value/act_q_sal_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_ebt_value/act_q_sal_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_ebt_value/act_q_sal_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_ner_value/act_q_sal_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_ner_value/act_q_sal_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_ner_value/act_q_sal_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_ner_value/act_q_sal_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_cps_value/act_12m_sal_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_cps_value/act_12m_sal_value, 100)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.747666
    headers: {'Date': 'Sat, 02 Aug 2025 17:20:39 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': 'af9b0c9e2eec4cee9ee7c901a9b2232b', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}

# INFO 2025-08-03 01:20:45,847
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# WARNING 2025-08-03 01:21:56,084
<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_ebt_value/act_q_sal_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_ebt_value/act_q_sal_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_ebt_value/act_q_sal_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_ebt_value/act_q_sal_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_ner_value/act_q_sal_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_ner_value/act_q_sal_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_ner_value/act_q_sal_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_ner_value/act_q_sal_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_cps_value/act_12m_sal_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_cps_value/act_12m_sal_value, 100)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.369650
    headers: {'Date': 'Sat, 02 Aug 2025 17:21:53 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '2f2f6c7282614ebe9d434381b5de66b8', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}

# WARNING 2025-08-03 01:22:07,417
<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_fcf_value/act_q_nav_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_fcf_value/act_q_nav_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_fcf_value/act_q_nav_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_fcf_value/act_q_nav_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_sal_value/act_12m_cpx_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_sal_value/act_12m_cpx_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_sal_value/act_12m_cpx_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_sal_value/act_12m_cpx_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_net_value/act_12m_cpx_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_net_value/act_12m_cpx_value, 100)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.648032
    headers: {'Date': 'Sat, 02 Aug 2025 17:22:05 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': 'b6eb3c76c50e45f3a64af8553916af90', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}

# INFO 2025-08-03 01:22:11,446
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# WARNING 2025-08-03 01:27:03,019
<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_ebi_value/act_q_cpx_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_ebi_value/act_q_cpx_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_ebi_value/act_q_cpx_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_ebi_value/act_q_cpx_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_ebt_value/act_q_cpx_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_ebt_value/act_q_cpx_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_ebt_value/act_q_cpx_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_ebt_value/act_q_cpx_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_cps_value/act_q_cpx_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_cps_value/act_q_cpx_value, 100)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.407151
    headers: {'Date': 'Sat, 02 Aug 2025 17:27:00 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '22f3434157b1435b8de0ab46c56b3422', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}

# INFO 2025-08-03 01:27:06,764
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# WARNING 2025-08-03 01:28:17,070
<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_ebi_value/act_q_cpx_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_ebi_value/act_q_cpx_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_ebi_value/act_q_cpx_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_ebi_value/act_q_cpx_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_ebt_value/act_q_cpx_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_ebt_value/act_q_cpx_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_ebt_value/act_q_cpx_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_ebt_value/act_q_cpx_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_cps_value/act_q_cpx_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_cps_value/act_q_cpx_value, 100)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.426221
    headers: {'Date': 'Sat, 02 Aug 2025 17:28:14 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '326dc22335554d8da9e3424468d327aa', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}

# INFO 2025-08-03 01:28:20,968
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# WARNING 2025-08-03 01:29:31,364
<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_ebi_value/act_q_cpx_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_ebi_value/act_q_cpx_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_ebi_value/act_q_cpx_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_ebi_value/act_q_cpx_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_ebt_value/act_q_cpx_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_ebt_value/act_q_cpx_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_ebt_value/act_q_cpx_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_ebt_value/act_q_cpx_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_cps_value/act_q_cpx_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_cps_value/act_q_cpx_value, 100)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.361687
    headers: {'Date': 'Sat, 02 Aug 2025 17:29:29 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '3125ed7f3e0c41a1b9c4255efaa5fcce', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}

# WARNING 2025-08-03 01:29:41,924
<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_dps_value/act_12m_bps_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_dps_value/act_12m_bps_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_dps_value/act_12m_nav_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_dps_value/act_12m_nav_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_dps_value/act_12m_nav_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_dps_value/act_12m_nav_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_dps_value/act_q_eps_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_dps_value/act_q_eps_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_dps_value/act_q_eps_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_dps_value/act_q_eps_value, 400)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.414023
    headers: {'Date': 'Sat, 02 Aug 2025 17:29:39 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': 'ca549a2dec5c4421b6d09d523e2bac88', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}

# INFO 2025-08-03 01:29:45,903
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# WARNING 2025-08-03 01:30:56,382
<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_dps_value/act_12m_bps_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_dps_value/act_12m_bps_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_dps_value/act_12m_nav_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_dps_value/act_12m_nav_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_dps_value/act_12m_nav_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_dps_value/act_12m_nav_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_dps_value/act_q_eps_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_dps_value/act_q_eps_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_dps_value/act_q_eps_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_dps_value/act_q_eps_value, 400)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.477604
    headers: {'Date': 'Sat, 02 Aug 2025 17:30:54 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '8d817ad6c9544a1d8c6d660cb395f8cb', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}

# INFO 2025-08-03 01:31:00,354
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# WARNING 2025-08-03 01:31:28,695
<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_dps_value/act_q_fcf_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_dps_value/act_q_fcf_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_dps_value/act_q_bps_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_dps_value/act_q_bps_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_dps_value/act_q_bps_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_dps_value/act_q_bps_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_dps_value/act_q_nav_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_dps_value/act_q_nav_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_dps_value/act_q_nav_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_dps_value/act_q_nav_value, 400)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.783740
    headers: {'Date': 'Sat, 02 Aug 2025 17:31:26 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': 'a0bf27202a8e4ee6a24aafff7732c961', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}

# INFO 2025-08-03 01:31:32,617
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# WARNING 2025-08-03 01:32:43,141
<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_dps_value/act_q_fcf_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_dps_value/act_q_fcf_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_dps_value/act_q_bps_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_dps_value/act_q_bps_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_dps_value/act_q_bps_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_dps_value/act_q_bps_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_dps_value/act_q_nav_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_dps_value/act_q_nav_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_dps_value/act_q_nav_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_dps_value/act_q_nav_value, 400)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.360195
    headers: {'Date': 'Sat, 02 Aug 2025 17:32:40 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '99efc6ea68bb4e5bbb0a4d6b647aadf9', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}

# INFO 2025-08-03 01:32:47,159
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# WARNING 2025-08-03 01:36:26,970
<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_opr_value/act_q_opr_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_opr_value/act_q_opr_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_opr_value/act_q_opr_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_opr_value/act_q_opr_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_ebi_value/act_q_ebi_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_ebi_value/act_q_ebi_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_ebi_value/act_q_ebi_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_ebi_value/act_q_ebi_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_ebt_value/act_q_ebt_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_ebt_value/act_q_ebt_value, 100)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.629683
    headers: {'Date': 'Sat, 02 Aug 2025 17:36:24 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': 'e32c5a002c0a4d16973ca745f56d7f5d', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}

# INFO 2025-08-03 01:36:30,752
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# WARNING 2025-08-03 01:37:51,258
<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_nav_value/act_q_nav_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_nav_value/act_q_nav_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_nav_value/act_q_nav_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_nav_value/act_q_nav_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_dps_value/act_q_dps_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_dps_value/act_q_dps_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_dps_value/act_q_dps_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_dps_value/act_q_dps_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_cpx_value/act_q_cpx_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_cpx_value/act_q_cpx_value, 100)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.445817
    headers: {'Date': 'Sat, 02 Aug 2025 17:37:49 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': 'bfea9d2dbbc04ec89a4a2b4ef57b0f3a', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}

# INFO 2025-08-03 01:37:55,025
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# WARNING 2025-08-03 01:39:05,141
<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_nav_value/act_q_nav_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_nav_value/act_q_nav_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_nav_value/act_q_nav_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_nav_value/act_q_nav_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_dps_value/act_q_dps_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_dps_value/act_q_dps_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_dps_value/act_q_dps_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_dps_value/act_q_dps_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_cpx_value/act_q_cpx_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_cpx_value/act_q_cpx_value, 100)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.359223
    headers: {'Date': 'Sat, 02 Aug 2025 17:39:02 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': 'ecf71ef9c18e4f79ac706d022013e576', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}

# INFO 2025-08-03 01:39:09,108
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# WARNING 2025-08-03 01:40:33,008
<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_grm_value/act_12m_roe_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_grm_value/act_12m_roe_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_grm_value/act_12m_eps_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_grm_value/act_12m_eps_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_grm_value/act_12m_eps_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_grm_value/act_12m_eps_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_grm_value/act_12m_bps_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_grm_value/act_12m_bps_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_grm_value/act_12m_bps_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_grm_value/act_12m_bps_value, 400)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.440875
    headers: {'Date': 'Sat, 02 Aug 2025 17:40:30 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '8e6ea561e93c40edb47e8314b1cef5f5', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}

# INFO 2025-08-03 01:40:36,913
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# WARNING 2025-08-03 01:41:47,421
<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_grm_value/act_12m_roe_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_grm_value/act_12m_roe_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_grm_value/act_12m_eps_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_grm_value/act_12m_eps_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_grm_value/act_12m_eps_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_grm_value/act_12m_eps_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_grm_value/act_12m_bps_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_grm_value/act_12m_bps_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_grm_value/act_12m_bps_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_grm_value/act_12m_bps_value, 400)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.369697
    headers: {'Date': 'Sat, 02 Aug 2025 17:41:45 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': 'abaf74a9ec5d4ad6a0d7b372feb9c304', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}

# INFO 2025-08-03 01:41:51,209
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# WARNING 2025-08-03 01:43:01,384
<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_grm_value/act_12m_roe_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_grm_value/act_12m_roe_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_grm_value/act_12m_eps_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_grm_value/act_12m_eps_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_grm_value/act_12m_eps_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_grm_value/act_12m_eps_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_grm_value/act_12m_bps_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_grm_value/act_12m_bps_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_grm_value/act_12m_bps_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_grm_value/act_12m_bps_value, 400)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.361849
    headers: {'Date': 'Sat, 02 Aug 2025 17:42:59 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': 'f395979075fc42f993e078e0b12040ab', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}

# WARNING 2025-08-03 01:43:11,779
<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_grm_value/act_12m_nav_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_grm_value/act_12m_nav_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_grm_value/act_12m_nav_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_grm_value/act_12m_nav_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_grm_value/act_q_roa_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_grm_value/act_q_roa_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_grm_value/act_q_roa_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_grm_value/act_q_roa_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_grm_value/act_q_roe_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_grm_value/act_q_roe_value, 100)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.357937
    headers: {'Date': 'Sat, 02 Aug 2025 17:43:09 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': 'd36bb64019634e269ec1637461e1abcd', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}

# INFO 2025-08-03 01:43:15,691
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# WARNING 2025-08-03 01:44:35,110
<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_roe_value/act_12m_roa_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_roe_value/act_12m_roa_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_roa_value/act_q_roe_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_roa_value/act_q_roe_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_roa_value/act_q_roe_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_roa_value/act_q_roe_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_roe_value/act_q_roa_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_roe_value/act_q_roa_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_roe_value/act_q_roa_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_roe_value/act_q_roa_value, 400)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.374420
    headers: {'Date': 'Sat, 02 Aug 2025 17:44:32 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': 'ce668a9bb2fc4d1c9fd14fba0fc0b52b', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}

# INFO 2025-08-03 01:44:38,921
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# WARNING 2025-08-03 01:57:52,236
<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_cps_value/act_q_opr_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_cps_value/act_q_opr_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_cps_value/act_q_opr_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_cps_value/act_q_opr_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_fcf_value/act_q_opr_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_fcf_value/act_q_opr_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_fcf_value/act_q_opr_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_fcf_value/act_q_opr_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_cps_value/act_q_ebi_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_cps_value/act_q_ebi_value, 100)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.381017
    headers: {'Date': 'Sat, 02 Aug 2025 17:57:49 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '1378e3f8df6245da84baa817e161673f', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}

# INFO 2025-08-03 01:57:56,182
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# WARNING 2025-08-03 01:58:37,610
<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_cps_value/act_q_ebi_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_cps_value/act_q_ebi_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_fcf_value/act_q_ebi_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_fcf_value/act_q_ebi_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_fcf_value/act_q_ebi_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_fcf_value/act_q_ebi_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_cpx_value/act_12m_sal_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_cpx_value/act_12m_sal_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_cpx_value/act_12m_sal_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_cpx_value/act_12m_sal_value, 400)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.377557
    headers: {'Date': 'Sat, 02 Aug 2025 17:58:35 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': 'd12a352d99404502ac5e70045b41151d', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}

# INFO 2025-08-03 01:58:41,548
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# WARNING 2025-08-03 02:00:04,558
<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_cpx_value/act_12m_cps_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_cpx_value/act_12m_cps_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_cpx_value/act_12m_cps_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_cpx_value/act_12m_cps_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_cpx_value/act_q_sal_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_cpx_value/act_q_sal_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_cpx_value/act_q_sal_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_cpx_value/act_q_sal_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_cpx_value/act_q_bps_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_cpx_value/act_q_bps_value, 100)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.386874
    headers: {'Date': 'Sat, 02 Aug 2025 18:00:02 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '6188e1295de04ccc868ff9fb2b932890', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}

# INFO 2025-08-03 02:00:08,551
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# WARNING 2025-08-03 02:10:04,356
<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_bps_value/act_12m_dps_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_bps_value/act_12m_dps_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_nav_value/act_12m_dps_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_nav_value/act_12m_dps_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_nav_value/act_12m_dps_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_nav_value/act_12m_dps_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_eps_value/act_q_dps_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_eps_value/act_q_dps_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_eps_value/act_q_dps_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_eps_value/act_q_dps_value, 400)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.393932
    headers: {'Date': 'Sat, 02 Aug 2025 18:10:01 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '53c14461c4674dfa8b0355fb1fafac1b', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}

# INFO 2025-08-03 02:10:08,494
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# WARNING 2025-08-03 02:11:05,614
<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_fcf_value/act_q_dps_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_fcf_value/act_q_dps_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_opr_value/act_q_dps_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_opr_value/act_q_dps_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_opr_value/act_q_dps_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_opr_value/act_q_dps_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_ebi_value/act_q_dps_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_ebi_value/act_q_dps_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_ebi_value/act_q_dps_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_ebi_value/act_q_dps_value, 400)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.373783
    headers: {'Date': 'Sat, 02 Aug 2025 18:11:03 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': 'aa4fced33c7c4bb892835c51e0b51f91', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}

# INFO 2025-08-03 02:11:09,627
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# WARNING 2025-08-03 02:11:21,181
<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_bps_value/act_12m_dps_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_bps_value/act_12m_dps_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_nav_value/act_12m_dps_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_nav_value/act_12m_dps_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_nav_value/act_12m_dps_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_nav_value/act_12m_dps_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_eps_value/act_q_dps_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_eps_value/act_q_dps_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_eps_value/act_q_dps_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_eps_value/act_q_dps_value, 400)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.404712
    headers: {'Date': 'Sat, 02 Aug 2025 18:11:18 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': 'd71fb4cbffeb484f970c575ccb308669', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}

# INFO 2025-08-03 02:11:25,165
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# WARNING 2025-08-03 02:14:46,943
<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_sal_value/act_12m_bps_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_sal_value/act_12m_bps_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_sal_value/act_12m_nav_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_sal_value/act_12m_nav_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_sal_value/act_12m_nav_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_sal_value/act_12m_nav_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_sal_value/act_12m_eps_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_sal_value/act_12m_eps_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_sal_value/act_12m_eps_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_sal_value/act_12m_eps_value, 400)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.376997
    headers: {'Date': 'Sat, 02 Aug 2025 18:14:44 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '1d67ea9f03a24dc991b30d37250898f6', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}

# INFO 2025-08-03 02:14:50,968
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# WARNING 2025-08-03 02:19:58,919
<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_gps_value/act_q_sal_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_gps_value/act_q_sal_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_gps_value/act_q_sal_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_gps_value/act_q_sal_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_eps_value/act_12m_roa_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_eps_value/act_12m_roa_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_eps_value/act_12m_roa_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_eps_value/act_12m_roa_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_net_value/act_12m_roa_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_net_value/act_12m_roa_value, 100)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.391282
    headers: {'Date': 'Sat, 02 Aug 2025 18:19:56 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '49a542374eb842bc8c3daf75a01ee865', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}

# INFO 2025-08-03 02:20:02,876
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# WARNING 2025-08-03 02:26:06,422
<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_cps_value/act_12m_grm_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_cps_value/act_12m_grm_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_eps_value/act_q_grm_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_eps_value/act_q_grm_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_eps_value/act_q_grm_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_eps_value/act_q_grm_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_net_value/act_q_grm_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_net_value/act_q_grm_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_net_value/act_q_grm_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_net_value/act_q_grm_value, 400)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.563816
    headers: {'Date': 'Sat, 02 Aug 2025 18:26:03 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '9bc9db5c53ba4435b0848e0347af9c2a', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}

# INFO 2025-08-03 02:26:10,451
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# WARNING 2025-08-03 02:29:25,605
<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_ebi_value/act_12m_pre_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_ebi_value/act_12m_pre_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_ebi_value/act_12m_pre_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_ebi_value/act_12m_pre_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_ebt_value/act_12m_prr_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_ebt_value/act_12m_prr_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_ebt_value/act_12m_prr_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_ebt_value/act_12m_prr_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_opr_value/act_12m_pre_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_opr_value/act_12m_pre_value, 100)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.383486
    headers: {'Date': 'Sat, 02 Aug 2025 18:29:23 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '22012ff9b6c64d59841e3706a48c47b6', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}

# INFO 2025-08-03 02:29:29,581
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# WARNING 2025-08-03 02:29:40,551
<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_opr_value/act_12m_pre_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_opr_value/act_12m_pre_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_net_value/act_12m_pre_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_net_value/act_12m_pre_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_net_value/act_12m_pre_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_net_value/act_12m_pre_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_ner_value/act_12m_prr_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_ner_value/act_12m_prr_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_ner_value/act_12m_prr_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_ner_value/act_12m_prr_value, 400)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.398939
    headers: {'Date': 'Sat, 02 Aug 2025 18:29:38 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '371ef289bf5143a9a32d525d78a38cb5', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}

# INFO 2025-08-03 02:29:44,512
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# WARNING 2025-08-03 02:30:54,756
<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_opr_value/act_12m_pre_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_opr_value/act_12m_pre_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_net_value/act_12m_pre_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_net_value/act_12m_pre_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_net_value/act_12m_pre_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_net_value/act_12m_pre_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_ner_value/act_12m_prr_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_ner_value/act_12m_prr_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_ner_value/act_12m_prr_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_ner_value/act_12m_prr_value, 400)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.372794
    headers: {'Date': 'Sat, 02 Aug 2025 18:30:52 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '9265f420f97142c3ab3a6f8fa629f0e1', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}

# INFO 2025-08-03 02:30:58,515
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# WARNING 2025-08-03 02:31:48,044
<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_net_value/act_q_pre_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_net_value/act_q_pre_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_net_value/act_q_pre_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_net_value/act_q_pre_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_ner_value/act_q_prr_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_ner_value/act_q_prr_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_ner_value/act_q_prr_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_ner_value/act_q_prr_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_sal_value/act_12m_cps_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_sal_value/act_12m_cps_value, 100)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.358916
    headers: {'Date': 'Sat, 02 Aug 2025 18:31:45 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '6a158a5cf12049639da3afa8f4f2c07c', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}

# INFO 2025-08-03 02:31:51,748
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# WARNING 2025-08-03 02:38:49,636
<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_ebt_value/act_q_cps_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_ebt_value/act_q_cps_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_ebt_value/act_q_fcf_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_ebt_value/act_q_fcf_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_ebt_value/act_q_fcf_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_ebt_value/act_q_fcf_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_roa_value/act_12m_eps_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_roa_value/act_12m_eps_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_roa_value/act_12m_eps_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_roa_value/act_12m_eps_value, 400)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.372781
    headers: {'Date': 'Sat, 02 Aug 2025 18:38:47 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '1a81ab36fd764cfea9a13002bd83ef13', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}

# INFO 2025-08-03 02:38:53,426
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# WARNING 2025-08-03 02:42:47,056
<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_roa_value/act_q_net_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_roa_value/act_q_net_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_roe_value/act_q_net_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_roe_value/act_q_net_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_roe_value/act_q_net_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_roe_value/act_q_net_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_roa_value/act_q_cps_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_roa_value/act_q_cps_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_roa_value/act_q_cps_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_roa_value/act_q_cps_value, 400)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.352649
    headers: {'Date': 'Sat, 02 Aug 2025 18:42:44 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '21db62cf89b9441199f49e1988871d9e', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}

# INFO 2025-08-03 02:42:50,962
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# WARNING 2025-08-03 02:58:35,527
<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_cps_value/act_q_roa_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_cps_value/act_q_roa_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_fcf_value/act_q_roa_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_fcf_value/act_q_roa_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_fcf_value/act_q_roa_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_fcf_value/act_q_roa_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_cps_value/act_q_roe_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_cps_value/act_q_roe_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_cps_value/act_q_roe_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_cps_value/act_q_roe_value, 400)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.395114
    headers: {'Date': 'Sat, 02 Aug 2025 18:58:33 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '4b4143ed150747bea43f603884b62c42', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}

# INFO 2025-08-03 02:58:39,491
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# WARNING 2025-08-03 03:13:41,002
<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_sal_value/act_q_dps_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_sal_value/act_q_dps_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_bps_value/act_12m_sal_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_bps_value/act_12m_sal_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_bps_value/act_12m_sal_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_bps_value/act_12m_sal_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_nav_value/act_12m_sal_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_nav_value/act_12m_sal_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_nav_value/act_12m_sal_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_nav_value/act_12m_sal_value, 400)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.638672
    headers: {'Date': 'Sat, 02 Aug 2025 19:13:38 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '4a84e3f1ee6942918661b670450a8674', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}

# INFO 2025-08-03 03:13:45,004
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# WARNING 2025-08-03 03:23:35,713
<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_roe_value/act_q_grm_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_roe_value/act_q_grm_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_bps_value/act_q_grm_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_bps_value/act_q_grm_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_bps_value/act_q_grm_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_bps_value/act_q_grm_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_nav_value/act_q_grm_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_nav_value/act_q_grm_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_nav_value/act_q_grm_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_nav_value/act_q_grm_value, 400)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.413411
    headers: {'Date': 'Sat, 02 Aug 2025 19:23:33 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '0e3ccdefcd7f44dab32a49f4f9f12e43', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}

# INFO 2025-08-03 03:23:39,447
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# WARNING 2025-08-03 03:27:55,746
<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_roa_value/act_q_sal_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_roa_value/act_q_sal_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_roe_value/act_q_sal_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_roe_value/act_q_sal_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_roe_value/act_q_sal_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_roe_value/act_q_sal_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_ndt_value/act_q_ebi_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_ndt_value/act_q_ebi_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_ndt_value/act_q_ebi_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_ndt_value/act_q_ebi_value, 400)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.334790
    headers: {'Date': 'Sat, 02 Aug 2025 19:27:53 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': 'e9ff3bf79ffd460a9c4c117be49e73e0', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}

# INFO 2025-08-03 03:27:59,645
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# WARNING 2025-08-03 03:46:31,591
<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_pre_value/act_12m_pre_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_pre_value/act_12m_pre_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_pre_value/act_12m_pre_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_pre_value/act_12m_pre_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_prr_value/act_12m_prr_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_prr_value/act_12m_prr_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_prr_value/act_12m_prr_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_prr_value/act_12m_prr_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_ner_value/act_12m_ner_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_ner_value/act_12m_ner_value, 100)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.354070
    headers: {'Date': 'Sat, 02 Aug 2025 19:46:29 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '6d492d58895c4f9ca2c7303e19beab06', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}

# INFO 2025-08-03 03:46:35,595
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# WARNING 2025-08-03 03:57:39,494
<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_eps_value/act_12m_pre_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_eps_value/act_12m_pre_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_eps_value/act_q_opr_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_eps_value/act_q_opr_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_eps_value/act_q_opr_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_eps_value/act_q_opr_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_eps_value/act_q_ebi_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_eps_value/act_q_ebi_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_eps_value/act_q_ebi_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_eps_value/act_q_ebi_value, 400)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.376243
    headers: {'Date': 'Sat, 02 Aug 2025 19:57:37 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '67e214263dc6401aa1d4f158836e5cd3', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}

# INFO 2025-08-03 03:57:43,444
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# WARNING 2025-08-03 03:58:53,671
<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_eps_value/act_12m_pre_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_eps_value/act_12m_pre_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_eps_value/act_q_opr_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_eps_value/act_q_opr_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_eps_value/act_q_opr_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_eps_value/act_q_opr_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_eps_value/act_q_ebi_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_eps_value/act_q_ebi_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_eps_value/act_q_ebi_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_eps_value/act_q_ebi_value, 400)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.363291
    headers: {'Date': 'Sat, 02 Aug 2025 19:58:51 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '1d51d0268f3e45b69dbd68820de1e72d', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}

# INFO 2025-08-03 03:58:57,405
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# WARNING 2025-08-03 04:08:36,117
<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_roe_value/act_q_dps_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_roe_value/act_q_dps_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_grm_value/act_12m_dps_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_grm_value/act_12m_dps_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_grm_value/act_12m_dps_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_12m_grm_value/act_12m_dps_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_grm_value/act_q_dps_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_grm_value/act_q_dps_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_grm_value/act_q_dps_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_grm_value/act_q_dps_value, 400)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.413073
    headers: {'Date': 'Sat, 02 Aug 2025 20:08:33 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '085f864cdaa54224892781e68638d489', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}

# INFO 2025-08-03 04:08:40,180
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# WARNING 2025-08-03 04:12:49,072
<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/simulations
    args: ()
    kwargs: {'data': None, 'json': [{'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_grm_value/act_q_cpx_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_grm_value/act_q_cpx_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_grm_value/act_q_cpx_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_grm_value/act_q_cpx_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_roa_value/act_q_ndt_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_roa_value/act_q_ndt_value, 100)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_roa_value/act_q_ndt_value, 200)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_roa_value/act_q_ndt_value, 400)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_roe_value/act_q_ndt_value, 20)'}, {'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'SUBINDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'visualization': False, 'maxTrade': 'OFF'}, 'regular': 'ts_std_dev(act_q_roe_value/act_q_ndt_value, 100)'}], 'timeout': 120}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/simulations
    elapsed: 0:00:00.374871
    headers: {'Date': 'Sat, 02 Aug 2025 20:12:46 GMT', 'Content-Type': 'application/json', 'Content-Length': '49', 'Connection': 'keep-alive', 'Allow': 'POST, OPTIONS', 'X-Request-Id': '25885778c32640cb97ab38610713bf2a', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {"detail":"CONCURRENT_SIMULATION_LIMIT_EXCEEDED"}

# INFO 2025-08-03 04:12:53,150
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-03 05:33:22,958
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-03 06:48:40,298
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-03 06:48:44,569
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-03 06:48:48,733
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-03 06:48:52,947
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-03 06:48:57,006
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-03 06:49:01,330
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-03 06:49:05,677
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-03 06:49:09,836
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-03 06:49:13,936
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-03 06:49:17,988
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-03 06:49:22,050
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-03 06:49:26,324
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-03 06:49:30,604
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-03 06:49:34,690
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-03 06:49:38,740
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# WARNING 2025-08-03 06:49:49,246
<WQBSession ['<EMAIL>']>.auth_request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'auth': <requests.auth.HTTPBasicAuth object at 0x150ac7710>}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.298538
    headers: {'Date': 'Sat, 02 Aug 2025 22:49:47 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '41', 'Connection': 'keep-alive', 'X-RateLimit-Limit-Minute': '5', 'RateLimit-Remaining': '0', 'RateLimit-Reset': '13', 'Retry-After': '13', 'RateLimit-Limit': '5', 'X-RateLimit-Remaining-Minute': '0', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {
  "message":"API rate limit exceeded"
}

# WARNING 2025-08-03 06:49:58,505
<WQBSession ['<EMAIL>']>.auth_request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'auth': <requests.auth.HTTPBasicAuth object at 0x150ac7710>}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.300025
    headers: {'Date': 'Sat, 02 Aug 2025 22:49:56 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '41', 'Connection': 'keep-alive', 'X-RateLimit-Limit-Minute': '5', 'RateLimit-Remaining': '0', 'RateLimit-Reset': '4', 'Retry-After': '4', 'RateLimit-Limit': '5', 'X-RateLimit-Remaining-Minute': '0', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {
  "message":"API rate limit exceeded"
}

# WARNING 2025-08-03 06:50:01,457
<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: GET
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'allow_redirects': True}
<Response [204]>:
    status_code: 204
    reason: No Content
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.303592
    headers: {'Date': 'Sat, 02 Aug 2025 22:49:58 GMT', 'Content-Length': '0', 'Connection': 'keep-alive', 'Allow': 'GET, POST, DELETE, HEAD, OPTIONS', 'X-Frame-Options': 'DENY', 'X-Content-Type-Options': 'nosniff', 'Referrer-Policy': 'same-origin', 'X-Request-ID': '********************************', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: 

# INFO 2025-08-03 06:50:01,458
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-03 06:50:05,803
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-03 06:50:09,994
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-03 06:50:14,091
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-03 06:50:18,299
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-03 06:50:22,492
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-03 06:50:26,742
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-03 06:50:30,975
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-03 06:50:39,936
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# WARNING 2025-08-03 06:50:50,919
<WQBSession ['<EMAIL>']>.auth_request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'auth': <requests.auth.HTTPBasicAuth object at 0x150a64d40>}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.297243
    headers: {'Date': 'Sat, 02 Aug 2025 22:50:49 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '41', 'Connection': 'keep-alive', 'X-RateLimit-Limit-Minute': '5', 'RateLimit-Remaining': '0', 'RateLimit-Reset': '11', 'Retry-After': '11', 'RateLimit-Limit': '5', 'X-RateLimit-Remaining-Minute': '0', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {
  "message":"API rate limit exceeded"
}

# INFO 2025-08-03 06:50:56,217
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-03 06:51:00,448
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-03 06:51:04,676
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-03 06:51:08,913
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-03 06:51:13,024
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-03 06:51:17,406
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-03 06:51:21,681
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-03 06:51:26,036
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-03 06:51:30,358
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-03 06:51:34,582
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-03 06:51:38,841
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# WARNING 2025-08-03 06:51:49,401
<WQBSession ['<EMAIL>']>.auth_request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'auth': <requests.auth.HTTPBasicAuth object at 0x150b3f7d0>}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.299883
    headers: {'Date': 'Sat, 02 Aug 2025 22:51:47 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '41', 'Connection': 'keep-alive', 'Retry-After': '13', 'X-RateLimit-Remaining-Minute': '0', 'RateLimit-Limit': '5', 'RateLimit-Remaining': '0', 'RateLimit-Reset': '13', 'X-RateLimit-Limit-Minute': '5', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {
  "message":"API rate limit exceeded"
}

# WARNING 2025-08-03 06:51:58,785
<WQBSession ['<EMAIL>']>.auth_request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'auth': <requests.auth.HTTPBasicAuth object at 0x150b3f7d0>}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.297066
    headers: {'Date': 'Sat, 02 Aug 2025 22:51:56 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '41', 'Connection': 'keep-alive', 'X-RateLimit-Limit-Minute': '5', 'RateLimit-Remaining': '0', 'RateLimit-Reset': '4', 'Retry-After': '4', 'RateLimit-Limit': '5', 'X-RateLimit-Remaining-Minute': '0', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {
  "message":"API rate limit exceeded"
}

# WARNING 2025-08-03 06:52:06,121
<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: GET
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'allow_redirects': True}
<Response [204]>:
    status_code: 204
    reason: No Content
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:04.938050
    headers: {'Date': 'Sat, 02 Aug 2025 22:51:59 GMT', 'Content-Length': '0', 'Connection': 'keep-alive', 'Allow': 'GET, POST, DELETE, HEAD, OPTIONS', 'X-Frame-Options': 'DENY', 'X-Content-Type-Options': 'nosniff', 'Referrer-Policy': 'same-origin', 'X-Request-ID': '5ce8c95a6418499fbeb94496de4caabe', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: 

# INFO 2025-08-03 06:52:06,122
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-03 06:52:10,427
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-03 06:52:14,574
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-03 06:52:18,626
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-03 06:52:22,989
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-03 06:52:27,435
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-03 06:52:34,078
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-03 06:52:38,186
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-03 06:52:45,035
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-03 06:52:49,332
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# WARNING 2025-08-03 06:52:59,884
<WQBSession ['<EMAIL>']>.auth_request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'auth': <requests.auth.HTTPBasicAuth object at 0x150b462d0>}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.311937
    headers: {'Date': 'Sat, 02 Aug 2025 22:52:57 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '41', 'Connection': 'keep-alive', 'Retry-After': '3', 'X-RateLimit-Remaining-Minute': '0', 'RateLimit-Limit': '5', 'RateLimit-Remaining': '0', 'RateLimit-Reset': '3', 'X-RateLimit-Limit-Minute': '5', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {
  "message":"API rate limit exceeded"
}

# INFO 2025-08-03 06:53:02,921
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-03 06:53:07,260
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-03 06:53:11,649
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-03 06:53:15,988
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-03 06:53:20,366
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-03 06:53:24,490
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-03 06:53:28,664
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-03 06:53:33,048
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-03 06:53:37,301
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-03 06:53:41,609
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# WARNING 2025-08-03 06:53:52,144
<WQBSession ['<EMAIL>']>.auth_request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'auth': <requests.auth.HTTPBasicAuth object at 0x150a01eb0>}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.300952
    headers: {'Date': 'Sat, 02 Aug 2025 22:53:50 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '41', 'Connection': 'keep-alive', 'Retry-After': '10', 'X-RateLimit-Remaining-Minute': '0', 'RateLimit-Limit': '5', 'RateLimit-Remaining': '0', 'RateLimit-Reset': '10', 'X-RateLimit-Limit-Minute': '5', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {
  "message":"API rate limit exceeded"
}

# WARNING 2025-08-03 06:54:01,370
<WQBSession ['<EMAIL>']>.auth_request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'auth': <requests.auth.HTTPBasicAuth object at 0x150a01eb0>}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.292873
    headers: {'Date': 'Sat, 02 Aug 2025 22:53:59 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '41', 'Connection': 'keep-alive', 'Retry-After': '1', 'X-RateLimit-Remaining-Minute': '0', 'RateLimit-Limit': '5', 'RateLimit-Remaining': '0', 'RateLimit-Reset': '1', 'X-RateLimit-Limit-Minute': '5', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {
  "message":"API rate limit exceeded"
}

# WARNING 2025-08-03 06:54:04,023
<WQBSession ['<EMAIL>']>.request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: GET
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'allow_redirects': True}
<Response [204]>:
    status_code: 204
    reason: No Content
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.296606
    headers: {'Date': 'Sat, 02 Aug 2025 22:54:01 GMT', 'Content-Length': '0', 'Connection': 'keep-alive', 'Allow': 'GET, POST, DELETE, HEAD, OPTIONS', 'X-Frame-Options': 'DENY', 'X-Content-Type-Options': 'nosniff', 'Referrer-Policy': 'same-origin', 'X-Request-ID': 'e3aef60c3a974c04bada70cd5bc88ff2', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: 

# INFO 2025-08-03 06:54:04,023
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-03 06:54:08,105
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-03 06:54:12,215
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-03 06:54:16,441
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-03 06:54:20,518
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-03 06:54:24,709
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-03 06:54:29,214
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-03 06:54:33,370
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-03 06:54:42,154
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-03 06:54:51,047
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# WARNING 2025-08-03 06:55:01,593
<WQBSession ['<EMAIL>']>.auth_request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'auth': <requests.auth.HTTPBasicAuth object at 0x150aaa180>}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.305648
    headers: {'Date': 'Sat, 02 Aug 2025 22:54:59 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '41', 'Connection': 'keep-alive', 'Retry-After': '1', 'X-RateLimit-Remaining-Minute': '0', 'RateLimit-Limit': '5', 'RateLimit-Remaining': '0', 'RateLimit-Reset': '1', 'X-RateLimit-Limit-Minute': '5', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {
  "message":"API rate limit exceeded"
}

# INFO 2025-08-03 06:55:04,632
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-03 06:55:08,896
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-03 06:55:13,207
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-03 06:55:17,331
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-03 06:55:21,568
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-03 06:55:25,884
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-03 06:55:29,888
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-03 06:55:34,305
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-03 06:55:38,365
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# INFO 2025-08-03 06:55:42,575
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

# WARNING 2025-08-03 06:55:52,918
<WQBSession ['<EMAIL>']>.auth_request(...) [max 3 tries ran out]
super().request(method, url, *args, **kwargs):
    method: POST
    url: https://api.worldquantbrain.com/authentication
    args: ()
    kwargs: {'auth': <requests.auth.HTTPBasicAuth object at 0x150a9ef90>}
<Response [429]>:
    status_code: 429
    reason: Too Many Requests
    url: https://api.worldquantbrain.com/authentication
    elapsed: 0:00:00.324846
    headers: {'Date': 'Sat, 02 Aug 2025 22:55:50 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '41', 'Connection': 'keep-alive', 'X-RateLimit-Limit-Minute': '5', 'RateLimit-Remaining': '0', 'RateLimit-Reset': '10', 'Retry-After': '10', 'RateLimit-Limit': '5', 'X-RateLimit-Remaining-Minute': '0', 'vary': 'Origin', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
    text: {
  "message":"API rate limit exceeded"
}

# INFO 2025-08-03 06:56:00,561
<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: 

